<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\User;
use App\Models\Department;
use App\Models\DepartmentSupervisorAssistant;
use App\Services\DepartmentTeamManagementService;
use Illuminate\Http\Request;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Carbon\Carbon;

class WorkflowTaskController extends Controller
{
    use AuthorizesRequests;

    /**
     * Display a listing of tasks.
     */
    public function index()
    {
        $this->authorize('assign-tasks');

        return view('workflow-tasks.index');
    }

    /**
     * Show the form for creating a new task.
     */
    public function create()
    {
        $this->authorize('create', Task::class);

        // Get all users who can be assigned tasks
        $users = User::whereHas('roles', function ($q) {
            $q->whereNotIn('name', ['admin']);
        })->select('id', 'name', 'email')->orderBy('name')->get();

        // If no users found with role filtering, get all users
        if ($users->isEmpty()) {
            $users = User::select('id', 'name', 'email')->orderBy('name')->get();
        }
        $departments = Department::active()->select('id', 'name')->orderBy('name')->get();

        // Get department team structures for better task assignment
        $departmentTeamService = app(DepartmentTeamManagementService::class);
        $departmentTeams = [];

        foreach ($departments as $department) {
            $departmentTeams[$department->id] = $departmentTeamService->getDepartmentTeamStructure($department);
        }

        return view('workflow-tasks.create', compact('users', 'departments', 'departmentTeams'));
    }

    /**
     * Store a newly created task.
     */
    public function store(Request $request)
    {
        $this->authorize('create', Task::class);
        
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:daily,weekly',
            'assigned_to' => 'required|exists:users,id',
            'department_id' => 'nullable|exists:departments,id',
            'due_date' => 'required|date|after_or_equal:today',
            'priority' => 'required|integer|min:1|max:3',
        ]);

        $validated['assigned_by'] = auth()->id();

        Task::create($validated);

        return redirect()->route('workflow-tasks.index')
            ->with('success', 'Task created successfully.');
    }

    /**
     * Display the specified task.
     */
    public function show(Task $task)
    {
        $this->authorize('view', $task);
        
        return view('workflow-tasks.show', compact('task'));
    }

    /**
     * Show the form for editing the task.
     */
    public function edit(Task $task)
    {
        $this->authorize('update', $task);
        
        $users = User::select('id', 'name', 'email')->orderBy('name')->get();
        $departments = Department::active()->select('id', 'name')->orderBy('name')->get();
        
        return view('workflow-tasks.edit', compact('task', 'users', 'departments'));
    }

    /**
     * Update the specified task.
     */
    public function update(Request $request, Task $task)
    {
        $this->authorize('update', $task);
        
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:daily,weekly',
            'status' => 'required|in:pending,in_progress,completed,cancelled',
            'assigned_to' => 'required|exists:users,id',
            'department_id' => 'nullable|exists:departments,id',
            'due_date' => 'required|date',
            'priority' => 'required|integer|min:1|max:3',
            'completion_notes' => 'nullable|string',
        ]);

        $task->update($validated);

        return redirect()->route('workflow-tasks.index')
            ->with('success', 'Task updated successfully.');
    }

    /**
     * Remove the specified task.
     */
    public function destroy(Task $task)
    {
        $this->authorize('delete', $task);
        
        $task->delete();

        return redirect()->route('workflow-tasks.index')
            ->with('success', 'Task deleted successfully.');
    }

    /**
     * Mark task as completed.
     */
    public function complete(Task $task)
    {
        $this->authorize('complete', $task);
        
        $task->markCompleted();

        return redirect()->route('workflow-tasks.index')
            ->with('success', 'Task marked as completed.');
    }

    /**
     * Change task status.
     */
    public function changeStatus(Request $request, Task $task)
    {
        $validated = $request->validate([
            'status' => 'required|in:pending,in_progress,completed,cancelled',
        ]);

        // Use the new changeStatus authorization method
        $this->authorize('changeStatus', [$task, $validated['status']]);

        $oldStatus = $task->status;
        $task->update($validated);

        // Handle task status propagation for grouped tasks
        if ($task->role_type === 'superior') {
            if ($validated['status'] === 'completed') {
                // If a superior completes a task, also complete related assistant tasks
                $this->completeRelatedAssistantTasks($task);
            } elseif ($oldStatus === 'completed' && $validated['status'] === 'in_progress') {
                // If a superior reverts a completed task, also revert related assistant tasks
                $this->revertRelatedAssistantTasks($task);
            }
        }

        return redirect()->back()->with('success', 'Task status updated successfully.');
    }

    /**
     * Get tasks for a specific user.
     */
    public function userTasks(User $user)
    {
        $this->authorize('viewPerformance', $user);
        
        $tasks = Task::where('assigned_to', $user->id)
            ->with(['assignedBy', 'department'])
            ->orderBy('due_date', 'asc')
            ->paginate(10);

        return view('workflow-tasks.user-tasks', compact('user', 'tasks'));
    }

    /**
     * Get task statistics.
     */
    public function statistics()
    {
        $this->authorize('assign-tasks');
        
        $user = auth()->user();
        
        $query = Task::query();
        
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $departmentIds = $user->departments->pluck('id');
                $query->where(function ($q) use ($user, $departmentIds) {
                    $q->where('assigned_by', $user->id)
                      ->orWhereIn('department_id', $departmentIds);
                });
            }
        }

        $stats = [
            'total_tasks' => $query->count(),
            'pending_tasks' => $query->where('status', 'pending')->count(),
            'in_progress_tasks' => $query->where('status', 'in_progress')->count(),
            'completed_tasks' => $query->where('status', 'completed')->count(),
            'overdue_tasks' => $query->where('due_date', '<', Carbon::today())
                                   ->whereNotIn('status', ['completed', 'cancelled'])
                                   ->count(),
            'daily_tasks' => $query->where('type', 'daily')->count(),
            'weekly_tasks' => $query->where('type', 'weekly')->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Get task summary with filtering options.
     */
    public function getSummary(Request $request)
    {
        // Allow all authenticated users to view summary, but filter data based on permissions
        
        $viewType = $request->get('view_type', 'overall'); // overall, department, superior
        $month = $request->get('month', 'current'); // current, previous, custom
        $startDate = $request->get('start_date');
        $endDate = $request->get('end_date');
        $status = $request->get('status', 'all');
        $department = $request->get('department');
        
        // Set date range based on month filter
        if ($month === 'current') {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
        } elseif ($month === 'previous') {
            $startDate = Carbon::now()->subMonth()->startOfMonth();
            $endDate = Carbon::now()->subMonth()->endOfMonth();
        } elseif ($month === 'custom' && $startDate && $endDate) {
            $startDate = Carbon::parse($startDate);
            $endDate = Carbon::parse($endDate);
        } else {
            $startDate = Carbon::now()->startOfMonth();
            $endDate = Carbon::now()->endOfMonth();
        }

        $user = auth()->user();
        $query = Task::query()->with(['assignedTo', 'assignedBy', 'department']);
        
        // Apply date filter
        $query->whereBetween('created_at', [$startDate, $endDate]);
        
        // Apply user permissions
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $departmentIds = $user->departments->pluck('id');
                $query->where(function ($q) use ($user, $departmentIds) {
                    $q->where('assigned_by', $user->id)
                      ->orWhereIn('department_id', $departmentIds);
                });
            }
        }
        
        // Apply status filter
        if ($status !== 'all') {
            $query->where('status', $status);
        }
        
        // Apply department filter
        if ($department) {
            $query->where('department_id', $department);
        }

        switch ($viewType) {
            case 'department':
                return $this->getDepartmentWiseSummary($query, $startDate, $endDate);
            case 'superior':
                return $this->getSuperiorWiseSummary($query, $startDate, $endDate);
            default:
                return $this->getOverallSummary($query, $startDate, $endDate);
        }
    }

    /**
     * Get overall summary statistics.
     */
    private function getOverallSummary($query, $startDate, $endDate)
    {
        $tasks = $query->get();
        
        return [
            'view_type' => 'overall',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'summary' => [
                'total' => $tasks->count(),
                'pending' => $tasks->where('status', 'pending')->count(),
                'in_progress' => $tasks->where('status', 'in_progress')->count(),
                'completed' => $tasks->where('status', 'completed')->count(),
                'cancelled' => $tasks->where('status', 'cancelled')->count(),
                'overdue' => $tasks->filter(function($task) {
                    return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                })->count()
            ]
        ];
    }

    /**
     * Get department-wise summary statistics.
     */
    private function getDepartmentWiseSummary($query, $startDate, $endDate)
    {
        $tasks = $query->get();
        $departments = Department::active()->get();
        
        $departmentSummary = [];
        
        foreach ($departments as $department) {
            $deptTasks = $tasks->where('department_id', $department->id);
            
            $departmentSummary[] = [
                'department' => [
                    'id' => $department->id,
                    'name' => $department->name
                ],
                'summary' => [
                    'total' => $deptTasks->count(),
                    'pending' => $deptTasks->where('status', 'pending')->count(),
                    'in_progress' => $deptTasks->where('status', 'in_progress')->count(),
                    'completed' => $deptTasks->where('status', 'completed')->count(),
                    'cancelled' => $deptTasks->where('status', 'cancelled')->count(),
                    'overdue' => $deptTasks->filter(function($task) {
                        return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                    })->count()
                ],
                'users' => $this->getDepartmentUsersSummary($deptTasks, $department)
            ];
        }
        
        // Add tasks without department
        $noDeptTasks = $tasks->whereNull('department_id');
        if ($noDeptTasks->count() > 0) {
            $departmentSummary[] = [
                'department' => [
                    'id' => null,
                    'name' => 'No Department'
                ],
                'summary' => [
                    'total' => $noDeptTasks->count(),
                    'pending' => $noDeptTasks->where('status', 'pending')->count(),
                    'in_progress' => $noDeptTasks->where('status', 'in_progress')->count(),
                    'completed' => $noDeptTasks->where('status', 'completed')->count(),
                    'cancelled' => $noDeptTasks->where('status', 'cancelled')->count(),
                    'overdue' => $noDeptTasks->filter(function($task) {
                        return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                    })->count()
                ],
                'users' => $this->getDepartmentUsersSummary($noDeptTasks, null)
            ];
        }
        
        return [
            'view_type' => 'department',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'departments' => $departmentSummary
        ];
    }

    /**
     * Get superior-wise summary statistics.
     */
    private function getSuperiorWiseSummary($query, $startDate, $endDate)
    {
        $tasks = $query->get();
        $superiors = User::whereHas('roles', function($q) {
            $q->where('name', 'Superior');
        })->with('assistants')->get();
        
        $superiorSummary = [];
        
        foreach ($superiors as $superior) {
            $superiorTasks = $tasks->where('assigned_to', $superior->id);
            
            $assistantsSummary = [];
            foreach ($superior->assistants as $assistant) {
                $assistantTasks = $tasks->where('assigned_to', $assistant->id);
                
                if ($assistantTasks->count() > 0) {
                    $assistantsSummary[] = [
                        'assistant' => [
                            'id' => $assistant->id,
                            'name' => $assistant->name,
                            'email' => $assistant->email
                        ],
                        'summary' => [
                            'total' => $assistantTasks->count(),
                            'pending' => $assistantTasks->where('status', 'pending')->count(),
                            'in_progress' => $assistantTasks->where('status', 'in_progress')->count(),
                            'completed' => $assistantTasks->where('status', 'completed')->count(),
                            'cancelled' => $assistantTasks->where('status', 'cancelled')->count(),
                            'overdue' => $assistantTasks->filter(function($task) {
                                return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                            })->count()
                        ]
                    ];
                }
            }
            
            $totalSuperiorTasks = $superiorTasks->count() + collect($assistantsSummary)->sum('summary.total');
            
            if ($totalSuperiorTasks > 0) {
                $superiorSummary[] = [
                    'superior' => [
                        'id' => $superior->id,
                        'name' => $superior->name,
                        'email' => $superior->email
                    ],
                    'superior_tasks' => [
                        'total' => $superiorTasks->count(),
                        'pending' => $superiorTasks->where('status', 'pending')->count(),
                        'in_progress' => $superiorTasks->where('status', 'in_progress')->count(),
                        'completed' => $superiorTasks->where('status', 'completed')->count(),
                        'cancelled' => $superiorTasks->where('status', 'cancelled')->count(),
                        'overdue' => $superiorTasks->filter(function($task) {
                            return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                        })->count()
                    ],
                    'team_summary' => [
                        'total' => $totalSuperiorTasks,
                        'pending' => $superiorTasks->where('status', 'pending')->count() + collect($assistantsSummary)->sum('summary.pending'),
                        'in_progress' => $superiorTasks->where('status', 'in_progress')->count() + collect($assistantsSummary)->sum('summary.in_progress'),
                        'completed' => $superiorTasks->where('status', 'completed')->count() + collect($assistantsSummary)->sum('summary.completed'),
                        'cancelled' => $superiorTasks->where('status', 'cancelled')->count() + collect($assistantsSummary)->sum('summary.cancelled'),
                        'overdue' => $superiorTasks->filter(function($task) {
                            return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                        })->count() + collect($assistantsSummary)->sum('summary.overdue')
                    ],
                    'assistants' => $assistantsSummary
                ];
            }
        }
        
        return [
            'view_type' => 'superior',
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d'),
                'label' => $startDate->format('M Y')
            ],
            'superiors' => $superiorSummary
        ];
    }

    /**
     * Get department users summary.
     */
    private function getDepartmentUsersSummary($tasks, $department)
    {
        $usersSummary = [];
        $userIds = $tasks->pluck('assigned_to')->unique();
        
        foreach ($userIds as $userId) {
            $user = User::find($userId);
            if ($user) {
                $userTasks = $tasks->where('assigned_to', $userId);
                
                $usersSummary[] = [
                    'user' => [
                        'id' => $user->id,
                        'name' => $user->name,
                        'email' => $user->email,
                        'role' => $user->isSuperior() ? 'Superior' : ($user->isMujeeb() ? 'Assistant' : 'Other')
                    ],
                    'summary' => [
                        'total' => $userTasks->count(),
                        'pending' => $userTasks->where('status', 'pending')->count(),
                        'in_progress' => $userTasks->where('status', 'in_progress')->count(),
                        'completed' => $userTasks->where('status', 'completed')->count(),
                        'cancelled' => $userTasks->where('status', 'cancelled')->count(),
                        'overdue' => $userTasks->filter(function($task) {
                            return $task->due_date < Carbon::today() && !in_array($task->status, ['completed', 'cancelled']);
                        })->count()
                    ]
                ];
            }
        }
        
        return $usersSummary;
    }

    /**
     * Get my tasks (for the authenticated user).
     */
    public function myTasks()
    {
        $tasks = Task::where('assigned_to', auth()->id())
            ->with(['assignedBy', 'department'])
            ->orderBy('due_date', 'asc')
            ->orderBy('priority', 'desc')
            ->paginate(10);

        return view('workflow-tasks.my-tasks', compact('tasks'));
    }

    /**
     * Complete related assistant tasks when superior completes a task.
     */
    private function completeRelatedAssistantTasks(Task $superiorTask)
    {
        // Find assistant tasks with the same task_number
        $assistantTasks = Task::where('task_number', $superiorTask->task_number)
            ->where('role_type', 'assistant')
            ->where('status', '!=', 'completed')
            ->get();

        foreach ($assistantTasks as $assistantTask) {
            $assistantTask->update([
                'status' => 'completed',
                'completed_at' => now(),
                'completion_notes' => 'Automatically completed when superior completed the task.'
            ]);
        }
    }

    /**
     * Revert related assistant tasks when superior reverts a completed task.
     */
    private function revertRelatedAssistantTasks(Task $superiorTask)
    {
        // Find assistant tasks with the same task_number that were auto-completed
        $assistantTasks = Task::where('task_number', $superiorTask->task_number)
            ->where('role_type', 'assistant')
            ->where('status', 'completed')
            ->where('completion_notes', 'like', '%Automatically completed when superior%')
            ->get();

        foreach ($assistantTasks as $assistantTask) {
            $assistantTask->update([
                'status' => 'in_progress',
                'completed_at' => null,
                'completion_notes' => null
            ]);
        }
    }
}
