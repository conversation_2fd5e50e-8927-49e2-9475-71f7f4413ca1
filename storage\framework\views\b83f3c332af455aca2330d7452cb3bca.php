<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header pb-0">
                    <div class="d-lg-flex">
                        <div>
                            <h5 class="mb-0">Workflow Task Management</h5>
                            <p class="text-sm mb-0">Manage tasks and assignments across departments</p>
                        </div>
                        <div class="ms-auto my-auto mt-lg-0 mt-4">
                            <div class="ms-auto my-auto">
                                <div class="btn-group me-2" role="group">
                                    <a href="<?php echo e(route('departments.index')); ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-building"></i>&nbsp;&nbsp;Departments
                                    </a>
                                    <a href="<?php echo e(route('supervisor-assistant-mapping')); ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-users"></i>&nbsp;&nbsp;Team Mapping
                                    </a>
                                </div>
                                <button wire:click="toggleViewMode" class="btn btn-outline-info btn-sm mb-0 me-2">
                                    <i class="fas fa-<?php echo e($viewMode === 'list' ? 'sitemap' : 'list'); ?>"></i>&nbsp;&nbsp;<?php echo e($viewMode === 'list' ? 'Hierarchy View' : 'List View'); ?>

                                </button>
                                <button wire:click="openCreateModal" class="btn bg-gradient-primary btn-sm mb-0">
                                    <i class="fas fa-plus"></i>&nbsp;&nbsp;Create Task
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <input wire:model.live="search" type="text" class="form-control" placeholder="Search tasks...">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterStatus" class="form-select">
                                    <option value="all">All Status</option>
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                    <option value="cancelled">Cancelled</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterDepartment" class="form-select">
                                    <option value="all">All Departments</option>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($department->id); ?>"><?php echo e($department->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterAssignedTo" class="form-select">
                                    <option value="all">All Users</option>
                                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterPriority" class="form-select">
                                    <option value="all">All Priorities</option>
                                    <option value="3">High</option>
                                    <option value="2">Medium</option>
                                    <option value="1">Low</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                                                <select wire:model.live="filterType" class="form-select">
                                    <option value="all">All Types</option>
                                    <option value="daily">Daily</option>
                                    <option value="weekly">Weekly</option>
                                    <option value="operational">Operational</option>
                                    <option value="one-time">One-time Task</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <select wire:model.live="filterRole" class="form-select">
                                    <option value="all">All Roles</option>
                                    <option value="superior">Superior</option>
                                    <option value="assistant">Assistant</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <div class="text-sm text-secondary">
                                    <?php echo e($tasks->total()); ?> tasks
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Tasks Table -->
    <!--[if BLOCK]><![endif]--><?php if($viewMode === 'list'): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body px-0 pb-2">
                                        <div class="p-0">
                        <table class="table align-items-center mb-0">
                            <thead>
                                <tr>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task #</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Task</th>
                                    <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Assigned To</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Dept/Type</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Priority</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Status</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Reports</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Due Date</th>
                                    <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $tasks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="p-1">
                                        <div class="d-flex px-2 py-1">
                                            <div class="d-flex flex-column justify-content-center">
                                                <h6 class="mb-0 text-xs font-weight-bold"><?php echo e($task->task_number); ?></h6>
                                                <!--[if BLOCK]><![endif]--><?php if(isset($task->grouped_tasks) && $task->grouped_tasks->count() > 1): ?>
                                                    <p class="text-xs text-info mb-0">
                                                        <i class="fas fa-users"></i> Group
                                                    </p>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        </div>
                                    </td>
                                    <td class="p-1">
                                        <div class="px-2 py-1">
                                            <h6 class="mb-0 text-xs text-truncate" title="<?php echo e($task->title); ?>"><?php echo e($task->title); ?></h6>
                                            <!--[if BLOCK]><![endif]--><?php if($task->description): ?>
                                                <p class="text-xs text-secondary mb-0 text-truncate" title="<?php echo e($task->description); ?>"><?php echo e($task->description); ?></p>
                                            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            <span class="badge badge-xs bg-gradient-<?php echo e($task->type === 'daily' ? 'info' : ($task->type === 'weekly' ? 'warning' : 'primary')); ?>">
                                                <?php echo e(ucfirst($task->type)); ?>

                                            </span>
                                        </div>
                                    </td>
                                    <td class="p-1">
                                        <!--[if BLOCK]><![endif]--><?php if(isset($task->grouped_tasks) && $task->grouped_tasks->count() > 1): ?>
                                            <!-- Grouped tasks -->
                                            <div class="px-2 py-1">
                                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $task->grouped_tasks->take(2); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $groupedTask): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="d-flex align-items-center mb-1" title="<?php echo e($groupedTask->assignedTo->name); ?> (<?php echo e(ucfirst($groupedTask->role_type)); ?>)">
                                                    <div class="avatar avatar-xs bg-gradient-<?php echo e($groupedTask->role_type === 'superior' ? 'success' : 'info'); ?> me-1">
                                                        <span class="text-white text-xs"><?php echo e(strtoupper(substr($groupedTask->assignedTo->name, 0, 1))); ?></span>
                                                    </div>
                                                    <span class="text-xs text-truncate"><?php echo e($groupedTask->assignedTo->name); ?></span>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                                <!--[if BLOCK]><![endif]--><?php if($task->grouped_tasks->count() > 2): ?>
                                                <span class="text-xs text-muted">+<?php echo e($task->grouped_tasks->count() - 2); ?></span>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                        <?php else: ?>
                                            <!-- Single assignee -->
                                            <div class="px-2 py-1" title="<?php echo e($task->assignedTo->name); ?> - <?php echo e($task->assignedTo->email); ?>">
                                                <div class="d-flex align-items-center">
                                                    <div class="avatar avatar-xs bg-gradient-info me-1">
                                                        <span class="text-white text-xs"><?php echo e(strtoupper(substr($task->assignedTo->name, 0, 1))); ?></span>
                                                    </div>
                                                    <div>
                                                        <p class="text-xs font-weight-bold mb-0 text-truncate"><?php echo e($task->assignedTo->name); ?></p>
                                                        <span class="badge badge-xs bg-gradient-<?php echo e($task->assignedTo->isSuperior() ? 'success' : 'info'); ?>">
                                                            <?php echo e($task->assignedTo->isSuperior() ? 'Sup' : 'Ast'); ?>

                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                    <td class="align-middle text-center text-xs p-1">
                                        <!--[if BLOCK]><![endif]--><?php if($task->type === 'operational'): ?>
                                            <span class="badge badge-sm bg-gradient-primary">Op</span>
                                        <?php elseif($task->department): ?>
                                            <span class="badge badge-sm bg-gradient-secondary text-truncate"><?php echo e($task->department->name); ?></span>
                                        <?php else: ?>
                                            <span class="text-secondary">-</span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                    <td class="align-middle text-center p-1">
                                        <span class="badge badge-sm bg-gradient-<?php echo e($this->getTaskPriorityColor($task->priority)); ?>">
                                            <?php echo e($task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low')); ?>

                                        </span>
                                    </td>
                                    <td class="align-middle text-center p-1">
                                        <div class="dropdown">
                                            <span class="badge badge-sm bg-gradient-<?php echo e($this->getTaskStatusColor($task->status)); ?> dropdown-toggle" 
                                                  data-bs-toggle="dropdown" style="cursor: pointer;">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                            </span>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item" href="#" wire:click="changeTaskStatus(<?php echo e($task->id); ?>, 'pending')">Pending</a></li>
                                                <li><a class="dropdown-item" href="#" wire:click="changeTaskStatus(<?php echo e($task->id); ?>, 'in_progress')">In Progress</a></li>
                                                <li><a class="dropdown-item" href="#" wire:click="changeTaskStatus(<?php echo e($task->id); ?>, 'completed')">Completed</a></li>
                                                <li><a class="dropdown-item" href="#" wire:click="changeTaskStatus(<?php echo e($task->id); ?>, 'cancelled')">Cancelled</a></li>
                                                
                                            </ul>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center text-xs p-1">
                                        <!--[if BLOCK]><![endif]--><?php if(isset($task->grouped_tasks) && $task->grouped_tasks->count() > 1): ?>
                                            <?php
                                                $totalPerformances = $task->grouped_tasks->sum('total_performances_count');
                                                $submittedPerformances = $task->grouped_tasks->sum('submitted_performances_count');
                                            ?>
                                        <?php else: ?>
                                            <?php
                                                $totalPerformances = $task->total_performances_count ?? 0;
                                                $submittedPerformances = $task->submitted_performances_count ?? 0;
                                            ?>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                                        <div class="text-center">
                                            <span class="text-xs font-weight-bold text-<?php echo e($submittedPerformances > 0 ? 'success' : 'secondary'); ?>">
                                                <?php echo e($submittedPerformances); ?>/<?php echo e($totalPerformances); ?>

                                            </span>
                                        </div>
                                    </td>
                                    <td class="align-middle text-center text-xs p-1">
                                        <span class="text-secondary text-xs font-weight-bold">
                                            <?php echo e($task->due_date->format('M d, Y')); ?>

                                        </span>
                                        <!--[if BLOCK]><![endif]--><?php if($task->due_date->isPast() && $task->status !== 'completed'): ?>
                                            <br><span class="badge badge-sm bg-gradient-danger">Overdue</span>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </td>
                                    <td class="align-middle text-center p-1">
                                        <div class="d-flex justify-content-center text-nowrap">
                                            <button wire:click="openEditModal(<?php echo e($task->id); ?>)"
                                                    class="btn btn-xs btn-outline-primary me-1 mb-0" title="Edit Task">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button wire:click="deleteTask(<?php echo e($task->id); ?>)"
                                                    wire:confirm="<?php if(isset($task->grouped_tasks) && $task->grouped_tasks->count() > 1): ?>Are you sure you want to delete this task group? This will delete all <?php echo e($task->grouped_tasks->count()); ?> related tasks.@elseAre you sure you want to delete this task?<?php endif; ?>"
                                                    class="btn btn-xs btn-outline-danger mb-0" title="Delete Task">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center py-4">
                                        <div class="text-center">
                                            <i class="fas fa-tasks fa-3x text-secondary mb-3"></i>
                                            <h6 class="text-secondary">No tasks found</h6>
                                            <p class="text-sm text-secondary">Create your first task to get started.</p>
                                        </div>
                                    </td>
                                </tr>
                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    <!--[if BLOCK]><![endif]--><?php if($tasks->hasPages()): ?>
                    <div class="px-3 py-2">
                        <?php echo e($tasks->links()); ?>

                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Hierarchy View -->
    <!--[if BLOCK]><![endif]--><?php if($viewMode === 'hierarchy'): ?>
    <div class="row mt-4">
        <div class="col-12">
            <!--[if BLOCK]><![endif]--><?php $__empty_1 = true; $__currentLoopData = $hierarchyData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hierarchyItem): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="card mb-4">
                <div class="card-header pb-0">
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-lg bg-gradient-success me-3">
                            <span class="text-white font-weight-bold">
                                <?php echo e(strtoupper(substr($hierarchyItem['supervisor']->name, 0, 2))); ?>

                            </span>
                        </div>
                        <div>
                            <h6 class="mb-0"><?php echo e($hierarchyItem['supervisor']->name); ?></h6>
                            <p class="text-sm text-secondary mb-0">
                                <span class="badge badge-sm bg-gradient-success">Superior</span>
                                <?php echo e($hierarchyItem['supervisor']->email); ?>

                            </p>
                        </div>
                        <div class="ms-auto">
                            <span class="badge badge-lg bg-gradient-info">
                                <?php echo e($hierarchyItem['supervisor_tasks']->count()); ?> Tasks
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Superior Tasks -->
                    <!--[if BLOCK]><![endif]--><?php if($hierarchyItem['supervisor_tasks']->count() > 0): ?>
                    <div class="mb-4">
                        <h6 class="text-sm font-weight-bold mb-2">Superior Tasks</h6>
                        <div class="row">
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $hierarchyItem['supervisor_tasks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="col-md-6 mb-3">
                                <div class="card border">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                            <h6 class="mb-0 text-sm"><?php echo e($task->title); ?></h6>
                                            <span class="badge badge-sm bg-gradient-<?php echo e($this->getTaskStatusColor($task->status)); ?>">
                                                <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                            </span>
                                        </div>
                                        <!--[if BLOCK]><![endif]--><?php if($task->description): ?>
                                            <p class="text-xs text-secondary mb-2"><?php echo e(Str::limit($task->description, 80)); ?></p>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <span class="badge badge-sm bg-gradient-<?php echo e($this->getTaskPriorityColor($task->priority)); ?>">
                                                    <?php echo e($task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low')); ?>

                                                </span>
                                                <!--[if BLOCK]><![endif]--><?php if($task->department): ?>
                                                    <span class="badge badge-sm bg-gradient-secondary"><?php echo e($task->department->name); ?></span>
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                            </div>
                                            <small class="text-secondary"><?php echo e($task->due_date->format('M d')); ?></small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                    <!-- Assistant Tasks -->
                    <!--[if BLOCK]><![endif]--><?php if(count($hierarchyItem['assistants']) > 0): ?>
                    <div>
                        <h6 class="text-sm font-weight-bold mb-3">Assistant Tasks</h6>
                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $hierarchyItem['assistants']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assistantData): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="mb-3">
                            <div class="d-flex align-items-center mb-2">
                                <div class="avatar avatar-sm bg-gradient-info me-2">
                                    <span class="text-white text-xs font-weight-bold">
                                        <?php echo e(strtoupper(substr($assistantData['assistant']->name, 0, 2))); ?>

                                    </span>
                                </div>
                                <div>
                                    <p class="text-sm font-weight-bold mb-0"><?php echo e($assistantData['assistant']->name); ?></p>
                                    <p class="text-xs text-secondary mb-0">
                                        <span class="badge badge-sm bg-gradient-info">Assistant</span>
                                        <?php echo e($assistantData['tasks']->count()); ?> Tasks
                                    </p>
                                </div>
                            </div>
                            <div class="row">
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $assistantData['tasks']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $task): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-4 mb-2">
                                    <div class="card border-light">
                                        <div class="card-body p-2">
                                            <div class="d-flex justify-content-between align-items-start mb-1">
                                                <h6 class="mb-0 text-xs"><?php echo e(Str::limit($task->title, 30)); ?></h6>
                                                <span class="badge badge-sm bg-gradient-<?php echo e($this->getTaskStatusColor($task->status)); ?>">
                                                    <?php echo e(ucfirst(str_replace('_', ' ', $task->status))); ?>

                                                </span>
                                            </div>
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span class="badge badge-sm bg-gradient-<?php echo e($this->getTaskPriorityColor($task->priority)); ?>">
                                                    <?php echo e($task->priority === 3 ? 'High' : ($task->priority === 2 ? 'Medium' : 'Low')); ?>

                                                </span>
                                                <small class="text-secondary"><?php echo e($task->due_date->format('M d')); ?></small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="fas fa-sitemap fa-3x text-secondary mb-3"></i>
                    <h6 class="text-secondary">No Superior/Assistant hierarchy found</h6>
                    <p class="text-sm text-secondary">Create tasks with Superior/Assistant assignments to see the hierarchy view.</p>
                </div>
            </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Flash Messages -->
    <!--[if BLOCK]><![endif]--><?php if(session()->has('message')): ?>
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            <?php echo e(session('message')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Create Task Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showCreateModal): ?>
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Create New Task</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="createTask">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Title *</label>
                                    <input type="text" wire:model="title" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter task title">
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Type *</label>
                                    <select wire:model="type" class="form-select <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" <?php if($assignment_type === 'operational'): ?> disabled <?php endif; ?>>
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="operational">Operational</option>
                                        <option value="one-time">One-time</option>

                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($assignment_type === 'operational'): ?>
                                        <small class="text-muted">Operational tasks are automatically set to operational type</small>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <select wire:model.live="department_id" class="form-select <?php $__errorArgs = ['department_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" <?php if($assignment_type === 'operational'): ?> disabled <?php endif; ?>>
                                        <option value="">Select Department</option>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($department->id); ?>"><?php echo e($department->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['department_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($assignment_type === 'operational'): ?>
                                        <small class="text-muted">Operational tasks are not department-related</small>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Assignment Type *</label>
                                    <select wire:model.live="assignment_type" class="form-select">
                                        <option value="direct">Direct Assignment</option>
                                        <!--[if BLOCK]><![endif]--><?php if(auth()->user()->isNazim()): ?>
                                            <option value="superior_assistant">Superior → Assistant</option>
                                            <option value="operational">Operational Task</option>
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php if($assignment_type === 'operational'): ?>
                                        <small class="text-muted">Operational tasks are not department-related and can be assigned to Superior/Assistant users</small>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>

                        <!--[if BLOCK]><![endif]--><?php if($assignment_type === 'direct'): ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label">Assign To *</label>
                                    <select wire:model="assigned_to" class="form-select <?php $__errorArgs = ['assigned_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <option value="">Select User</option>
                                        <!--[if BLOCK]><![endif]--><?php if($department_id): ?>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getDepartmentTeamMembers($department_id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($member->id); ?>"><?php echo e($member->name); ?> (<?php echo e($member->email); ?>)</option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php else: ?>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?> (<?php echo e($user->email); ?>)</option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['assigned_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if($assignment_type === 'superior_assistant'): ?>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Superior *</label>
                                    <select wire:model.live="superior_id" class="form-select <?php $__errorArgs = ['superior_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <option value="">Select Superior</option>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getDepartmentSuperiors(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $superior): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($superior->id); ?>"><?php echo e($superior->name); ?> (<?php echo e($superior->email); ?>)</option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['superior_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    <!--[if BLOCK]><![endif]--><?php if($department_id): ?>
                                        <small class="text-muted">Showing superiors from selected department only</small>
                                    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Assistant (Optional)</label>
                                    <select wire:model="assistant_id" class="form-select <?php $__errorArgs = ['assistant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <option value="">Select Assistant</option>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getDepartmentAssistants(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assistant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($assistant->id); ?>"><?php echo e($assistant->name); ?> (<?php echo e($assistant->email); ?>)</option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['assistant_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    <small class="text-muted">
                                        <!--[if BLOCK]><![endif]--><?php if($superior_id): ?>
                                            Showing assistants assigned to selected superior
                                        <?php else: ?>
                                            Select a superior to see available assistants
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <!--[if BLOCK]><![endif]--><?php if($assignment_type === 'operational'): ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="form-label">Assign To *</label>
                                    <select wire:model="assigned_to" class="form-select <?php $__errorArgs = ['assigned_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" style="max-height: 200px; overflow-y: auto;">
                                        <option value="">Select User (<?php echo e($this->getOperationalTaskUsers()->count()); ?> available)</option>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getOperationalTaskUsers(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($user->id); ?>">
                                                <?php echo e($user->name); ?>

                                                <!--[if BLOCK]><![endif]--><?php if($user->isSuperior()): ?>
                                                    (Superior)
                                                <?php elseif($user->isMujeeb()): ?>
                                                    (Assistant)
                                                <?php else: ?>
                                                    (User)
                                                <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                                - <?php echo e($user->email); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['assigned_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Operational tasks can be assigned to any user (<?php echo e($this->getOperationalTaskUsers()->count()); ?> users available).
                                        Use the dropdown to scroll through all users.
                                    </small>
                                </div>
                            </div>
                        </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Priority *</label>
                                    <select wire:model="priority" class="form-select <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <option value="1">Low</option>
                                        <option value="2">Medium</option>
                                        <option value="3">High</option>
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Due Date *</label>
                                    <input type="date" wire:model="due_date" class="form-control <?php $__errorArgs = ['due_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['due_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea wire:model="description" class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="3" placeholder="Enter task description"></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn bg-gradient-primary" wire:click="createTask">
                        <i class="fas fa-plus"></i> Create Task
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->

    <!-- Edit Task Modal -->
    <!--[if BLOCK]><![endif]--><?php if($showEditModal): ?>
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Task</h5>
                    <button type="button" class="btn-close" wire:click="closeModals"></button>
                </div>
                <div class="modal-body">
                    <form wire:submit.prevent="updateTask">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Title *</label>
                                    <input type="text" wire:model="title" class="form-control <?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" placeholder="Enter task title">
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Task Type *</label>
                                    <select wire:model="type" class="form-select <?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <option value="daily">Daily</option>
                                        <option value="weekly">Weekly</option>
                                        <option value="operational">Operational</option>
                                        <option value="one-time">One-time Task</option>
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['type'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Department</label>
                                    <select wire:model.live="department_id" class="form-select <?php $__errorArgs = ['department_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <option value="">Select Department</option>
                                        <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $departments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $department): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($department->id); ?>"><?php echo e($department->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['department_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Assign To *</label>
                                    <select wire:model="assigned_to" class="form-select <?php $__errorArgs = ['assigned_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <option value="">Select User</option>
                                        <!--[if BLOCK]><![endif]--><?php if($department_id): ?>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $this->getDepartmentTeamMembers($department_id); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $member): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($member->id); ?>"><?php echo e($member->name); ?> (<?php echo e($member->email); ?>)</option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php else: ?>
                                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($user->id); ?>"><?php echo e($user->name); ?> (<?php echo e($user->email); ?>)</option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['assigned_to'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Priority *</label>
                                    <select wire:model="priority" class="form-select <?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                        <option value="1">Low</option>
                                        <option value="2">Medium</option>
                                        <option value="3">High</option>
                                    </select>
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['priority'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="form-label">Due Date *</label>
                                    <input type="date" wire:model="due_date" class="form-control <?php $__errorArgs = ['due_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>">
                                    <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['due_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Description</label>
                            <textarea wire:model="description" class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" rows="3" placeholder="Enter task description"></textarea>
                            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> <div class="invalid-feedback"><?php echo e($message); ?></div> <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" wire:click="closeModals">Cancel</button>
                    <button type="button" class="btn bg-gradient-primary" wire:click="updateTask">
                        <i class="fas fa-save"></i> Update Task
                    </button>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div><?php /**PATH D:\laravel\fatawa-checking-system-mufti-ali-asghar\resources\views/livewire/workflow-task-management.blade.php ENDPATH**/ ?>