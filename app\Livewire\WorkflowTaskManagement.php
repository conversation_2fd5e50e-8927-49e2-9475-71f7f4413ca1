<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Task;
use App\Models\User;
use App\Models\Department;
use App\Models\DepartmentSupervisorAssistant;
use App\Services\DepartmentTeamManagementService;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class WorkflowTaskManagement extends Component
{
    use WithPagination, AuthorizesRequests;

    public $search = '';
    public $filterStatus = 'all';
    public $filterDepartment = 'all';
    public $filterAssignedTo = 'all';
    public $filterPriority = 'all';
    public $filterRole = 'all'; // New filter for Superior/Assistant roles
    public $filterType = 'all'; // New filter for task types (daily, weekly, operational)
    public $viewMode = 'list'; // list or hierarchy
    
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showAssignModal = false;
    
    // Task form properties
    public $taskId;
    public $title = '';
    public $description = '';
    public $type = 'daily';
    public $status = 'pending';
    public $assigned_to = '';
    public $department_id = '';
    public $due_date = '';
    public $priority = 1;
    public $superior_id = ''; // For Superior selection
    public $assistant_id = ''; // For Assistant selection
    public $assignment_type = 'direct'; // direct, superior_assistant, or operational
    
    public $departments = [];
    public $users = [];
    public $departmentTeams = [];
    public $superiors = [];
    public $assistants = [];
    public $availableAssistants = []; // Assistants available for selected superior
    
    protected $departmentTeamService;

    protected $rules = [
        'title' => 'required|string|max:255',
        'description' => 'nullable|string',
        'type' => 'required|in:daily,weekly,operational,one-time',
        'department_id' => 'nullable|exists:departments,id',
        'due_date' => 'required|date',
        'priority' => 'required|integer|min:1|max:3',
        'assignment_type' => 'required|in:direct,superior_assistant,operational',
    ];

    protected function rules()
    {
        $rules = [
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|in:daily,weekly,operational,one-time',
            'department_id' => 'nullable|exists:departments,id',
            'due_date' => 'required|date',
            'priority' => 'required|integer|min:1|max:3',
            'assignment_type' => 'required|in:direct,superior_assistant,operational',
        ];

        if ($this->assignment_type === 'direct') {
            $rules['assigned_to'] = 'required|exists:users,id';
        } elseif ($this->assignment_type === 'superior_assistant') {
            $rules['superior_id'] = 'required|exists:users,id';
            $rules['assistant_id'] = 'nullable|exists:users,id';
        } elseif ($this->assignment_type === 'operational') {
            $rules['assigned_to'] = 'required|exists:users,id';
            // For operational tasks, department is not required
            $rules['department_id'] = 'nullable';
        }

        return $rules;
    }

    public function boot(DepartmentTeamManagementService $departmentTeamService)
    {
        $this->departmentTeamService = $departmentTeamService;
    }

    public function mount()
    {
        $this->authorize('assign-tasks');
        $this->loadData();
    }

    public function render()
    {
        $query = Task::query()
            ->with(['assignedTo', 'assignedBy', 'department', 'childTasks', 'parentTask'])
            ->withCount([
                'dailyPerformances as total_performances_count',
                'dailyPerformances as submitted_performances_count' => function ($query) {
                    $query->where('is_submitted', true);
                }
            ])
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('title', 'like', '%' . $this->search . '%')
                      ->orWhere('description', 'like', '%' . $this->search . '%')
                      ->orWhere('task_number', 'like', '%' . $this->search . '%')
                      ->orWhereHas('assignedTo', function ($userQuery) {
                          $userQuery->where('name', 'like', '%' . $this->search . '%');
                      });
                });
            })
            ->when($this->filterStatus !== 'all', function ($query) {
                $query->where('status', $this->filterStatus);
            })
            ->when($this->filterDepartment !== 'all', function ($query) {
                $query->where('department_id', $this->filterDepartment);
            })
            ->when($this->filterAssignedTo !== 'all', function ($query) {
                $query->where('assigned_to', $this->filterAssignedTo);
            })
            ->when($this->filterPriority !== 'all', function ($query) {
                $query->where('priority', $this->filterPriority);
            })
            ->when($this->filterType !== 'all', function ($query) {
                $query->where('type', $this->filterType);
            })
            ->when($this->filterRole !== 'all', function ($query) {
                if ($this->filterRole === 'superior') {
                    $query->where('role_type', 'superior');
                } elseif ($this->filterRole === 'assistant') {
                    $query->where('role_type', 'assistant');
                }
            })
            ->when(!auth()->user()->isNazim(), function ($query) {
                // Role-based access control
                $user = auth()->user();
                if ($user->isSuperior()) {
                    // Superior can see tasks assigned to them and their assistants
                    $assistantIds = $user->assistants->pluck('id');
                    $departmentUserIds = $user->supervisorDepartments->flatMap->teamMembers->pluck('id');
                    $allowedUserIds = collect([$user->id])->merge($assistantIds)->merge($departmentUserIds)->unique();

                    $query->where(function ($q) use ($user, $allowedUserIds) {
                        $q->where('assigned_by', $user->id)
                          ->orWhereIn('assigned_to', $allowedUserIds);
                    });
                } elseif ($user->isMujeeb()) {
                    // Assistant can only see tasks assigned to them
                    $query->where('assigned_to', $user->id);
                }
            });

        // Group tasks by task_number to show as single entries
        $allTasks = $query->orderBy('due_date', 'asc')
                         ->orderBy('priority', 'desc')
                         ->get();

        // Group tasks by task_number and show only parent tasks (or single tasks)
        $groupedTasks = $allTasks->groupBy('task_number')->map(function ($taskGroup) {
            // Return the parent task if exists, otherwise the first task
            $parentTask = $taskGroup->where('role_type', 'superior')->first() 
                         ?? $taskGroup->where('parent_task_id', null)->first()
                         ?? $taskGroup->first();
            
            // Attach child tasks for display
            $parentTask->grouped_tasks = $taskGroup;
            return $parentTask;
        })->values();

        // Paginate the grouped results
        $currentPage = request()->get('page', 1);
        $perPage = 15;
        $tasks = new \Illuminate\Pagination\LengthAwarePaginator(
            $groupedTasks->forPage($currentPage, $perPage),
            $groupedTasks->count(),
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'pageName' => 'page']
        );

        $hierarchyData = $this->viewMode === 'hierarchy' ? $this->getHierarchyViewData() : [];

        return view('livewire.workflow-task-management', [
            'tasks' => $tasks,
            'hierarchyData' => $hierarchyData,
        ]);
    }

    public function loadData()
    {
        $this->departments = Department::active()->orderBy('name')->get();

        $user = auth()->user();

        if ($user->isNazim()) {
            // Nazim can assign tasks to anyone
            $this->users = User::whereHas('roles', function ($q) {
                $q->whereNotIn('name', ['admin']);
            })->orderBy('name')->get();

            // Load Superior and Assistant users separately for better organization
            $this->superiors = User::whereHas('roles', function ($q) {
                $q->where('name', 'Superior');
            })->orderBy('name')->get();

            $this->assistants = User::whereHas('roles', function ($q) {
                $q->whereIn('name', ['mujeeb', 'Muawin']);
            })->orderBy('name')->get();

        } elseif ($user->isSuperior()) {
            // Superior can assign tasks to their assistants and department members
            $assistantIds = $user->assistants->pluck('id');
            $departmentUserIds = $user->supervisorDepartments->flatMap->teamMembers->pluck('id');
            $userIds = collect([$user->id])->merge($assistantIds)->merge($departmentUserIds)->unique();

            $this->users = User::whereIn('id', $userIds)
                ->orderBy('name')
                ->get();

            $this->superiors = collect([$user]); // Only themselves
            $this->assistants = $user->assistants;
        } else {
            // Regular users can't assign tasks, only view their own
            $this->users = collect();
            $this->superiors = collect();
            $this->assistants = collect();
        }

        // If no users found with role filtering, get all users (fallback)
        if ($this->users->isEmpty() && $user->isNazim()) {
            $this->users = User::orderBy('name')->get();
        }

        // Load department teams
        $this->departmentTeams = [];
        foreach ($this->departments as $department) {
            $this->departmentTeams[$department->id] = $this->departmentTeamService->getDepartmentTeamStructure($department);
        }
    }

    public function openCreateModal()
    {
        $this->authorize('create', Task::class);
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function createTask()
    {
        $this->authorize('create', Task::class);
        $this->validate();

        // Generate unique task number
        $taskNumber = Task::generateTaskNumber();

        // Handle different assignment types
        if ($this->assignment_type === 'operational') {
            // Operational tasks - not department related, direct assignment
            Task::create([
                'task_number' => $taskNumber,
                'title' => $this->title,
                'description' => $this->description,
                'type' => 'operational', // Force operational type
                'assigned_to' => $this->assigned_to,
                'assigned_by' => auth()->id(),
                'department_id' => null, // Operational tasks are not department-related
                'due_date' => $this->due_date,
                'priority' => $this->priority,
                'role_type' => 'direct',
            ]);

            session()->flash('message', 'Operational task created successfully.');
        } elseif ($this->assignment_type === 'superior_assistant' && $this->superior_id) {
            // Create main task for Superior first
            $superiorTask = Task::create([
                'task_number' => $taskNumber,
                'title' => $this->title,
                'description' => $this->description,
                'type' => $this->type,
                'assigned_to' => $this->superior_id,
                'assigned_by' => auth()->id(),
                'department_id' => $this->department_id ?: null,
                'due_date' => $this->due_date,
                'priority' => $this->priority,
                'role_type' => 'superior',
            ]);

            // Create related task for Assistant if selected
            if ($this->assistant_id) {
                Task::create([
                    'task_number' => $taskNumber, // Same task number
                    'parent_task_id' => $superiorTask->id,
                    'title' => $this->title,
                    'description' => $this->description,
                    'type' => $this->type,
                    'assigned_to' => $this->assistant_id,
                    'assigned_by' => auth()->id(),
                    'department_id' => $this->department_id ?: null,
                    'due_date' => $this->due_date,
                    'priority' => $this->priority,
                    'role_type' => 'assistant',
                ]);
            }

            session()->flash('message', 'Task created successfully for Superior' . ($this->assistant_id ? ' and Assistant' : '') . '.');
        } else {
            // Direct assignment
            Task::create([
                'task_number' => $taskNumber,
                'title' => $this->title,
                'description' => $this->description,
                'type' => $this->type,
                'assigned_to' => $this->assigned_to,
                'assigned_by' => auth()->id(),
                'department_id' => $this->department_id ?: null,
                'due_date' => $this->due_date,
                'priority' => $this->priority,
                'role_type' => 'direct',
            ]);

            session()->flash('message', 'Task created successfully.');
        }

        $this->showCreateModal = false;
        $this->resetForm();
    }

    public function openEditModal($taskId)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('update', $task);
        
        $this->taskId = $task->id;
        $this->title = $task->title;
        $this->description = $task->description;
        $this->type = $task->type;
        $this->assigned_to = $task->assigned_to;
        $this->department_id = $task->department_id;
        $this->due_date = $task->due_date->format('Y-m-d');
        $this->priority = $task->priority;
        
        $this->showEditModal = true;
    }

    public function updateTask()
    {
        $task = Task::findOrFail($this->taskId);
        $this->authorize('update', $task);
        $this->validate();

        $task->update([
            'title' => $this->title,
            'description' => $this->description,
            'type' => $this->type,
            'assigned_to' => $this->assigned_to,
            'department_id' => $this->department_id ?: null,
            'due_date' => $this->due_date,
            'priority' => $this->priority,
        ]);

        $this->showEditModal = false;
        $this->resetForm();
        session()->flash('message', 'Task updated successfully.');
    }

    public function changeTaskStatus($taskId, $status)
    {
        $task = Task::findOrFail($taskId);

        // Use the new changeStatus authorization method
        $this->authorize('changeStatus', [$task, $status]);

        $oldStatus = $task->status;
        $task->update(['status' => $status]);

        if ($status === 'completed') {
            $task->update(['completed_at' => now()]);

            // If a superior completes a task, also complete related assistant tasks
            if ($task->role_type === 'superior') {
                $this->completeRelatedAssistantTasks($task);
            }
        }

        // Handle task status propagation for grouped tasks
        if ($task->role_type === 'superior') {
            if ($oldStatus === 'completed' && $status === 'in_progress') {
                // If a superior reverts a completed task, also revert related assistant tasks
                $this->revertRelatedAssistantTasks($task);
            }
        }

        session()->flash('message', 'Task status updated successfully.');
    }

    public function deleteTask($taskId)
    {
        $task = Task::findOrFail($taskId);
        $this->authorize('delete', $task);
        
        // Get all related tasks (including the task itself)
        $relatedTasks = $task->relatedTasks();
        $taskNumber = $task->task_number;
        $deletedCount = $relatedTasks->count();
        
        // Delete all related tasks in the group
        foreach ($relatedTasks as $relatedTask) {
            $relatedTask->delete();
        }
        
        // Show appropriate message based on whether it was a grouped task or single task
        if ($deletedCount > 1) {
            session()->flash('message', "Task group {$taskNumber} deleted successfully ({$deletedCount} tasks removed).");
        } else {
            session()->flash('message', 'Task deleted successfully.');
        }
    }

    public function resetForm()
    {
        $this->taskId = null;
        $this->title = '';
        $this->description = '';
        $this->type = 'daily';
        $this->assigned_to = '';
        $this->department_id = '';
        $this->due_date = '';
        $this->priority = 1;
        $this->superior_id = '';
        $this->assistant_id = '';
        $this->assignment_type = 'direct';
        $this->availableAssistants = [];
        $this->resetValidation();
    }

    /**
     * Get assistants for a selected superior
     */
    public function getAssistantsForSuperior($supervisorId)
    {
        if (!$supervisorId) return collect();

        $supervisor = User::find($supervisorId);
        if (!$supervisor) return collect();

        return $supervisor->assistants;
    }

    /**
     * Update available assistants when superior is selected
     */
    public function updatedAssignedTo($value)
    {
        if ($value) {
            $user = User::find($value);
            if ($user && $user->isSuperior()) {
                $this->availableAssistants = $this->getAssistantsForSuperior($value)->toArray();
            } else {
                $this->availableAssistants = [];
            }
        }
    }

    /**
     * Update available assistants when superior is selected in Superior/Assistant mode
     */
    public function updatedSuperiorId($value)
    {
        if ($value) {
            $this->availableAssistants = $this->getAssistantsForSuperior($value)->toArray();
            $this->assistant_id = ''; // Reset assistant selection
        } else {
            $this->availableAssistants = [];
            $this->assistant_id = '';
        }
    }

    /**
     * Handle assignment type change
     */
    public function updatedAssignmentType($value)
    {
        // Reset form fields when switching assignment types
        $this->assigned_to = '';
        $this->superior_id = '';
        $this->assistant_id = '';
        $this->availableAssistants = [];

        // For operational tasks, force type to operational and clear department
        if ($value === 'operational') {
            $this->type = 'operational';
            $this->department_id = '';
        }
    }

    /**
     * Handle department change - reset superior/assistant selections
     */
    public function updatedDepartmentId($value)
    {
        // Reset superior and assistant selections when department changes
        $this->superior_id = '';
        $this->assistant_id = '';
        $this->availableAssistants = [];
    }

    /**
     * Get superiors for the selected department
     */
    public function getDepartmentSuperiors()
    {
        if (!$this->department_id) {
            // If no department selected, return all superiors
            return $this->superiors;
        }

        // Get users who are specifically assigned as supervisors to this department
        $department = Department::find($this->department_id);
        if ($department) {
            return $department->supervisors()->orderBy('name')->get();
        }

        // Fallback to role-based filtering if department relationships not found
        return User::whereHas('roles', function ($q) {
            $q->where('name', 'Superior');
        })->whereHas('departments', function ($q) {
            $q->where('departments.id', $this->department_id);
        })->orderBy('name')->get();
    }

    /**
     * Get assistants for the selected superior and department
     */
    public function getDepartmentAssistants()
    {
        if (!$this->superior_id) {
            return collect();
        }

        // If department is selected, get assistants assigned to the superior within that department
        if ($this->department_id) {
            $department = Department::find($this->department_id);
            if ($department) {
                // Get assistants from the department who are assigned to the selected superior
                $departmentAssistants = $department->assistants();
                
                // Filter to only those assigned to the selected superior
                return $departmentAssistants->wherePivot('supervisor_id', $this->superior_id)
                    ->orderBy('name')->get();
            }
        }

        // Fallback: return all assistants for the selected superior
        return $this->getAssistantsForSuperior($this->superior_id);
    }

    /**
     * Get users available for operational task assignment (all users)
     */
    public function getOperationalTaskUsers()
    {
        $user = auth()->user();
        
        if ($user->isNazim()) {
            // Nazim can assign operational tasks to anyone
            return User::whereHas('roles', function ($q) {
                $q->whereNotIn('name', ['admin']);
            })->orderBy('name')->get();
        } elseif ($user->isSuperior()) {
            // Superior can assign operational tasks to their team members
            $assistantIds = $user->assistants->pluck('id');
            $departmentUserIds = $user->supervisorDepartments->flatMap->teamMembers->pluck('id');
            $userIds = collect([$user->id])->merge($assistantIds)->merge($departmentUserIds)->unique();
            
            return User::whereIn('id', $userIds)->orderBy('name')->get();
        } else {
            // Regular users can't assign operational tasks
            return collect();
        }
    }

    public function closeModals()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showAssignModal = false;
        $this->resetForm();
    }

    public function updatedFilterDepartment()
    {
        $this->resetPage();

        // Reset other filters when department changes
        if ($this->filterDepartment !== 'all') {
            // Keep only department filter active
            $this->filterAssignedTo = 'all';
        }
    }

    /**
     * Get tasks for a specific department
     */
    public function getDepartmentTasks($departmentId)
    {
        if (!$departmentId || $departmentId === 'all') {
            return Task::with(['assignedTo', 'assignedBy', 'department'])->get();
        }

        return Task::where('department_id', $departmentId)
            ->with(['assignedTo', 'assignedBy', 'department'])
            ->get();
    }

    /**
     * Get department structure with tasks
     */
    public function getDepartmentTaskStructure($departmentId)
    {
        $department = Department::findOrFail($departmentId);
        $teamStructure = $this->departmentTeamService->getDepartmentTeamStructure($department);
        $tasks = $this->getDepartmentTasks($departmentId);

        $result = [];

        foreach ($teamStructure as $team) {
            $supervisorId = $team['supervisor']->id;
            $supervisorTasks = $tasks->where('assigned_to', $supervisorId);

            $assistantData = [];
            foreach ($team['assistants'] as $assistant) {
                $assistantTasks = $tasks->where('assigned_to', $assistant->id);
                $assistantData[] = [
                    'assistant' => $assistant,
                    'tasks' => $assistantTasks
                ];
            }

            $result[] = [
                'supervisor' => $team['supervisor'],
                'supervisor_tasks' => $supervisorTasks,
                'assistants' => $assistantData
            ];
        }

        return $result;
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedFilterType()
    {
        $this->resetPage();
    }

    public function getTaskPriorityColor($priority)
    {
        return match($priority) {
            1 => 'success',
            2 => 'warning',
            3 => 'danger',
            default => 'secondary'
        };
    }

    public function getTaskStatusColor($status)
    {
        return match($status) {
            'pending' => 'secondary',
            'in_progress' => 'info',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'secondary'
        };
    }

    public function getDepartmentTeamMembers($departmentId)
    {
        if (!$departmentId || !isset($this->departmentTeams[$departmentId])) {
            return collect();
        }
        
        $members = collect();
        foreach ($this->departmentTeams[$departmentId] as $team) {
            $members->push($team['supervisor']);
            $members = $members->merge($team['assistants']);
        }
        
        return $members;
    }

    /**
     * Get hierarchy view data for Superior/Assistant task organization
     */
    public function getHierarchyViewData()
    {
        $user = auth()->user();
        $query = Task::with(['assignedTo', 'assignedBy', 'department']);

        // Apply role-based access control
        if (!$user->isNazim()) {
            if ($user->isSuperior()) {
                $assistantIds = $user->assistants->pluck('id');
                $departmentUserIds = $user->supervisorDepartments->flatMap->teamMembers->pluck('id');
                $allowedUserIds = collect([$user->id])->merge($assistantIds)->merge($departmentUserIds)->unique();

                $query->where(function ($q) use ($user, $allowedUserIds) {
                    $q->where('assigned_by', $user->id)
                      ->orWhereIn('assigned_to', $allowedUserIds);
                });
            } elseif ($user->isMujeeb()) {
                $query->where('assigned_to', $user->id);
            }
        }

        // Apply filters
        if ($this->search) {
            $query->where(function ($q) {
                $q->where('title', 'like', '%' . $this->search . '%')
                  ->orWhere('description', 'like', '%' . $this->search . '%')
                  ->orWhereHas('assignedTo', function ($userQuery) {
                      $userQuery->where('name', 'like', '%' . $this->search . '%');
                  });
            });
        }

        if ($this->filterStatus !== 'all') {
            $query->where('status', $this->filterStatus);
        }

        if ($this->filterDepartment !== 'all') {
            $query->where('department_id', $this->filterDepartment);
        }

        if ($this->filterPriority !== 'all') {
            $query->where('priority', $this->filterPriority);
        }

        $tasks = $query->get();

        // Group tasks by Superior/Assistant hierarchy
        $hierarchy = [];

        // Get all superiors who have tasks
        $superiorTasks = $tasks->filter(function ($task) {
            return $task->assignedTo->isSuperior();
        });

        $supervisorIds = $superiorTasks->pluck('assigned_to')->unique();

        foreach ($supervisorIds as $supervisorId) {
            $supervisor = User::find($supervisorId);
            if (!$supervisor) continue;

            $supervisorTasks = $tasks->where('assigned_to', $supervisorId);
            $assistants = $supervisor->assistants;

            $assistantData = [];
            foreach ($assistants as $assistant) {
                $assistantTasks = $tasks->where('assigned_to', $assistant->id);
                if ($assistantTasks->count() > 0) {
                    $assistantData[] = [
                        'assistant' => $assistant,
                        'tasks' => $assistantTasks
                    ];
                }
            }

            $hierarchy[] = [
                'supervisor' => $supervisor,
                'supervisor_tasks' => $supervisorTasks,
                'assistants' => $assistantData
            ];
        }

        return $hierarchy;
    }

    /**
     * Toggle view mode between list and hierarchy
     */
    public function toggleViewMode()
    {
        $this->viewMode = $this->viewMode === 'list' ? 'hierarchy' : 'list';
    }

    /**
     * Complete related assistant tasks when superior completes a task.
     */
    private function completeRelatedAssistantTasks(Task $superiorTask)
    {
        // Find assistant tasks with the same task_number
        $assistantTasks = Task::where('task_number', $superiorTask->task_number)
            ->where('role_type', 'assistant')
            ->where('status', '!=', 'completed')
            ->get();

        foreach ($assistantTasks as $assistantTask) {
            $assistantTask->update([
                'status' => 'completed',
                'completed_at' => now(),
                'completion_notes' => 'Automatically completed when superior completed the task.'
            ]);
        }
    }

    /**
     * Revert related assistant tasks when superior reverts a completed task.
     */
    private function revertRelatedAssistantTasks(Task $superiorTask)
    {
        // Find assistant tasks with the same task_number that were auto-completed
        $assistantTasks = Task::where('task_number', $superiorTask->task_number)
            ->where('role_type', 'assistant')
            ->where('status', 'completed')
            ->where('completion_notes', 'like', '%Automatically completed when superior%')
            ->get();

        foreach ($assistantTasks as $assistantTask) {
            $assistantTask->update([
                'status' => 'in_progress',
                'completed_at' => null,
                'completion_notes' => null
            ]);
        }
    }
}
